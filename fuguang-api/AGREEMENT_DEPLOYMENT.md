# 浮光APP协议功能部署说明

## 功能概述

本次实现了APP登录页面的服务协议和隐私协议功能，包括：

1. **后端协议接口**：提供公共的协议获取接口，无需登录即可访问
2. **APP端协议功能**：登录页面从后端获取协议内容并显示
3. **Web端协议管理**：管理员可以在web端添加和编辑协议内容
4. **菜单集成**：将协议管理功能集成到web端管理菜单中

## 部署步骤

### 1. 数据库配置

执行以下SQL脚本添加菜单配置：

```bash
# 在MySQL中执行
mysql -u root -p your_database < sql/agreement_menu.sql
```

### 2. 后端服务

确保以下文件已正确部署：

- `ruoyi-app/src/main/java/com/ruoyi/app/controller/AppAgreementController.java`
- `ruoyi-app/src/main/java/com/ruoyi/app/config/AppSecurityConfig.java`（已更新白名单）
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/fuguang/AppConfigManageController.java`

重启后端服务以加载新的控制器和安全配置。

### 3. Web端管理界面

确保以下文件已部署：

- `src/api/fuguang/agreement.js`
- `src/views/fuguang/agreement/index.vue`

重新构建并部署web端应用。

### 4. APP端功能

APP端的协议功能已经实现，包括：

- `api/auth.js`：协议获取接口
- `pages/login/login.vue`：登录页面协议功能
- `utils/request.js`：公共请求方法

## 功能验证

### 1. 后端接口测试

```bash
# 给测试脚本添加执行权限
chmod +x test_agreement.sh

# 运行测试脚本
./test_agreement.sh
```

### 2. Web端管理测试

1. 登录web管理端
2. 导航到"浮光管理 > 协议管理"
3. 点击"添加用户协议"或"添加隐私协议"按钮
4. 填写协议内容并保存
5. 验证协议列表显示正确

### 3. APP端功能测试

1. 启动APP并进入登录页面
2. 查看页面底部的协议勾选区域
3. 点击"《服务协议》"和"《隐私协议》"链接
4. 验证弹窗显示正确的协议内容
5. 测试勾选协议时的隐私保护提示

## API接口说明

### 公共协议接口（无需认证）

- `GET /app/agreement/user` - 获取用户协议
- `GET /app/agreement/privacy` - 获取隐私协议

### 管理接口（需要认证）

- `GET /fuguang/config/list` - 获取APP配置列表
- `GET /fuguang/config/{id}` - 获取指定配置详情
- `POST /fuguang/config` - 新增APP配置
- `PUT /fuguang/config` - 修改APP配置
- `DELETE /fuguang/config/{ids}` - 删除APP配置

## 权限配置

协议管理功能使用以下权限标识：

- `fuguang:config:list` - 查看协议列表
- `fuguang:config:query` - 查询协议详情
- `fuguang:config:add` - 添加协议
- `fuguang:config:edit` - 编辑协议
- `fuguang:config:remove` - 删除协议
- `fuguang:config:export` - 导出协议

## 注意事项

1. **安全配置**：协议接口已添加到APP安全配置的白名单中，无需认证即可访问
2. **数据初始化**：首次部署时，数据库中可能没有协议数据，需要通过web端管理界面添加
3. **缓存更新**：如果使用了Redis缓存，修改协议后可能需要清除相关缓存
4. **跨域配置**：确保APP端能够正常访问后端协议接口

## 故障排除

### 1. APP端无法获取协议内容

- 检查后端服务是否正常运行
- 验证协议接口是否在安全配置白名单中
- 确认数据库中是否有协议数据

### 2. Web端协议管理页面无法访问

- 检查菜单SQL是否正确执行
- 验证用户是否有相应权限
- 确认路由配置是否正确

### 3. 协议内容显示异常

- 检查协议内容是否包含特殊字符
- 验证前端显示组件是否正确处理长文本
- 确认数据库字段长度是否足够

## 后续扩展

1. **富文本编辑**：可以集成富文本编辑器，支持更丰富的协议内容格式
2. **版本管理**：添加协议版本管理功能，记录协议变更历史
3. **多语言支持**：支持多语言协议内容
4. **协议签署记录**：记录用户协议签署历史
