#!/bin/bash

# 协议功能测试脚本
echo "=== 浮光APP协议功能测试 ==="

# 设置API基础URL
BASE_URL="http://localhost:8888"

echo ""
echo "1. 测试获取用户协议接口（无需认证）"
curl -X GET "${BASE_URL}/app/agreement/user" \
  -H "Content-Type: application/json" \
  | jq '.'

echo ""
echo "2. 测试获取隐私协议接口（无需认证）"
curl -X GET "${BASE_URL}/app/agreement/privacy" \
  -H "Content-Type: application/json" \
  | jq '.'

echo ""
echo "3. 测试APP配置管理接口（需要认证）"
echo "请先在web端登录获取token，然后替换下面的TOKEN变量"

# 这里需要替换为实际的token
TOKEN="your_token_here"

if [ "$TOKEN" != "your_token_here" ]; then
  echo "测试获取APP配置列表"
  curl -X GET "${BASE_URL}/fuguang/config/list" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer ${TOKEN}" \
    | jq '.'
else
  echo "请先设置有效的TOKEN"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "手动测试步骤："
echo "1. 启动后端服务"
echo "2. 执行 agreement_menu.sql 脚本添加菜单"
echo "3. 登录web管理端，查看'浮光管理 > 协议管理'菜单"
echo "4. 在协议管理页面添加用户协议和隐私协议"
echo "5. 启动APP，在登录页面测试协议获取功能"
echo "6. 点击协议链接查看协议内容是否正确显示"
