package com.ruoyi.app.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.service.IAppConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * APP协议接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP协议接口")
@RestController("appAgreementController")
@RequestMapping("/app/agreement")
public class AppAgreementController extends BaseController
{
    @Autowired
    private IAppConfigService appConfigService;

    /**
     * 获取用户协议
     * 
     * @return 用户协议内容
     */
    @ApiOperation("获取用户协议")
    @GetMapping("/user")
    public AjaxResult getUserAgreement()
    {
        String userAgreement = appConfigService.getUserAgreement();
        return success(userAgreement);
    }

    /**
     * 获取隐私协议
     * 
     * @return 隐私协议内容
     */
    @ApiOperation("获取隐私协议")
    @GetMapping("/privacy")
    public AjaxResult getPrivacyPolicy()
    {
        String privacyPolicy = appConfigService.getPrivacyPolicy();
        return success(privacyPolicy);
    }
}
